#property strict

#include "../../module/mql4-lib-master/Trade/OrderGroup.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組類型枚舉                                           |
//+------------------------------------------------------------------+
enum ENUM_MARTINGALE_GROUP_TYPE
{
   MARTINGALE_TYPE_STANDARD,     // 標準馬丁格爾
   MARTINGALE_TYPE_REVERSE,      // 反向馬丁格爾
   MARTINGALE_TYPE_GRID,         // 網格馬丁格爾
   MARTINGALE_TYPE_PROGRESSIVE,  // 漸進式馬丁格爾
   MARTINGALE_TYPE_CUSTOM        // 自定義馬丁格爾
};

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組 - 管理馬丁格爾策略的訂單                           |
//+------------------------------------------------------------------+
class MartingaleOrderGroup : public OrderGroup
{
private:
   // 訂單組識別信息
   int               m_groupId;            // 訂單組獨立ID
   ENUM_MARTINGALE_GROUP_TYPE m_groupType; // 訂單組類型

   // 馬丁格爾策略參數
   bool              m_isReversed;         // 是否為反向馬丁

   // ID生成相關
   static int        s_nextGroupId;        // 靜態ID計數器

public:
   // 構造函數
   MartingaleOrderGroup(string symbol,
                        ENUM_MARTINGALE_GROUP_TYPE groupType = MARTINGALE_TYPE_STANDARD,
                        bool isReversed = false)
      : OrderGroup(symbol),
        m_groupId(GenerateGroupId()),
        m_groupType(groupType),
        m_isReversed(isReversed) {}

   // 獲取訂單組當前層級
   int               groupCurrentLevel() const;

   // 獲取訂單組盈虧平衡價格
   double            groupBreakEvenPrice() const;

   // 獲取訂單組ID
   int               groupId() const { return m_groupId; }

   // 獲取訂單組類型
   ENUM_MARTINGALE_GROUP_TYPE groupType() const { return m_groupType; }

   // 獲取訂單組類型字符串描述
   string            groupTypeString() const;

   // 獲取訂單最低價
   double            groupLowestPrice() const;

   // 獲取訂單最高價
   double            groupHighestPrice() const;

   // 獲取訂單最高價和最低價範圍價
   double            groupPriceRange() const;

   // 獲取訂單組總訂單數
   int               groupOrderTotal() const;

   // 使用 groupDoubleProperty 的便利方法
   double            groupCommission() { return groupDoubleProperty(Order::Commission); }
   double            groupSwap() { return groupDoubleProperty(Order::Swap); }

   // 組合盈虧（盈利 + 佣金 + 隔夜利息）
   double            groupNetProfit();

private:
   // 生成唯一的訂單組ID
   static int        GenerateGroupId();

   // 初始化靜態ID計數器
   static void       InitializeIdCounter();
};

// 靜態成員初始化
static int MartingaleOrderGroup::s_nextGroupId = 10000;

//+------------------------------------------------------------------+
//| 實現方法                                                         |
//+------------------------------------------------------------------+

// 生成唯一的訂單組ID
static int MartingaleOrderGroup::GenerateGroupId()
{
   return ++s_nextGroupId;
}

// 初始化靜態ID計數器
static void MartingaleOrderGroup::InitializeIdCounter()
{
   s_nextGroupId = 10000 + (int)(TimeCurrent() % 1000);
}

// 獲取訂單組當前層級
int MartingaleOrderGroup::groupCurrentLevel() const
{
   int openOrders = 0;

   // 計算未平倉訂單數量
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         if(OrderCloseTime() == 0) // 未平倉訂單
         {
            openOrders++;
         }
      }
   }

   return openOrders;
}

//+------------------------------------------------------------------+
//| 獲取訂單組盈虧平衡價格                                           |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::groupBreakEvenPrice() const
{
   if(size() == 0)
   {
      return 0.0;
   }

   double totalLots = 0.0;
   double weightedPrice = 0.0;
   int digits = (int)SymbolInfoInteger(Symbol(), SYMBOL_DIGITS);

   // 遍歷所有訂單計算加權平均價格
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         if(OrderCloseTime() == 0) // 只計算未平倉訂單
         {
            double lots = OrderLots();
            double openPrice = OrderOpenPrice();

            totalLots += lots;
            weightedPrice += lots * openPrice;
         }
      }
   }

   if(totalLots > 0)
   {
      return NormalizeDouble(weightedPrice / totalLots, digits);
   }

   return 0.0;
}

//+------------------------------------------------------------------+
//| 獲取訂單組類型字符串描述                                         |
//+------------------------------------------------------------------+
string MartingaleOrderGroup::groupTypeString() const
{
   switch(m_groupType)
   {
      case MARTINGALE_TYPE_STANDARD:
         return "標準馬丁格爾";
      case MARTINGALE_TYPE_REVERSE:
         return "反向馬丁格爾";
      case MARTINGALE_TYPE_GRID:
         return "網格馬丁格爾";
      case MARTINGALE_TYPE_PROGRESSIVE:
         return "漸進式馬丁格爾";
      case MARTINGALE_TYPE_CUSTOM:
         return "自定義馬丁格爾";
      default:
         return "未知類型";
   }
}

//+------------------------------------------------------------------+
//| 獲取訂單最低價                                                   |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::groupLowestPrice() const
{
   if(size() == 0)
   {
      return 0.0;
   }

   double lowestPrice = DBL_MAX;
   bool hasOpenOrder = false;

   // 遍歷所有訂單找到最低開倉價格
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         if(OrderCloseTime() == 0) // 只計算未平倉訂單
         {
            double openPrice = OrderOpenPrice();
            if(openPrice < lowestPrice)
            {
               lowestPrice = openPrice;
            }
            hasOpenOrder = true;
         }
      }
   }

   return hasOpenOrder ? lowestPrice : 0.0;
}

//+------------------------------------------------------------------+
//| 獲取訂單最高價                                                   |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::groupHighestPrice() const
{
   if(size() == 0)
   {
      return 0.0;
   }

   double highestPrice = 0.0;
   bool hasOpenOrder = false;

   // 遍歷所有訂單找到最高開倉價格
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         if(OrderCloseTime() == 0) // 只計算未平倉訂單
         {
            double openPrice = OrderOpenPrice();
            if(openPrice > highestPrice)
            {
               highestPrice = openPrice;
            }
            hasOpenOrder = true;
         }
      }
   }

   return hasOpenOrder ? highestPrice : 0.0;
}

//+------------------------------------------------------------------+
//| 獲取訂單最高價和最低價範圍價                                     |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::groupPriceRange() const
{
   double highestPrice = groupHighestPrice();
   double lowestPrice = groupLowestPrice();

   if(highestPrice > 0.0 && lowestPrice > 0.0)
   {
      return MathAbs(highestPrice - lowestPrice);
   }

   return 0.0;
}

//+------------------------------------------------------------------+
//| 獲取訂單組總訂單數                                               |
//+------------------------------------------------------------------+
int MartingaleOrderGroup::groupOrderTotal() const
{
   int totalOrders = 0;

   // 計算所有訂單數量（包括已平倉和未平倉）
   for(int i = 0; i < size(); i++)
   {
      int ticket = get(i);
      if(OrderSelect(ticket, SELECT_BY_TICKET))
      {
         totalOrders++;
      }
   }

   return totalOrders;
}

//+------------------------------------------------------------------+
//| 獲取訂單組淨盈虧（盈利 + 佣金 + 隔夜利息）                       |
//+------------------------------------------------------------------+
double MartingaleOrderGroup::groupNetProfit()
{
   return groupProfit() + groupCommission() + groupSwap();
}