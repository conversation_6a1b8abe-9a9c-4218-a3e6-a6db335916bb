#property strict

#include "../MartingaleOrderGroup.mqh"
#include "../../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組管理器介面                                         |
//+------------------------------------------------------------------+
interface IMartingaleGroupManager
{
public:
   // 註冊訂單組
   virtual bool RegisterGroup(MartingaleOrderGroup* group) = 0;
   
   // 註銷訂單組
   virtual bool UnregisterGroup(int groupId) = 0;
   
   // 根據ID獲取訂單組
   virtual MartingaleOrderGroup* GetGroup(int groupId) = 0;
   
   // 根據類型獲取訂單組列表
   virtual Vector<MartingaleOrderGroup*>* GetGroupsByType(ENUM_MARTINGALE_GROUP_TYPE groupType) = 0;
   
   // 根據交易品種獲取訂單組列表
   virtual Vector<MartingaleOrderGroup*>* GetGroupsBySymbol(string symbol) = 0;
   
   // 根據魔術數字獲取訂單組列表
   virtual Vector<MartingaleOrderGroup*>* GetGroupsByMagic(int magicNumber) = 0;
   
   // 獲取所有訂單組
   virtual Vector<MartingaleOrderGroup*>* GetAllGroups() = 0;
   
   // 關閉所有訂單組
   virtual bool CloseAllGroups() = 0;
   
   // 關閉指定類型的所有訂單組
   virtual bool CloseGroupsByType(ENUM_MARTINGALE_GROUP_TYPE groupType) = 0;
   
   // 更新所有訂單組狀態
   virtual void UpdateAllGroups() = 0;
   
   // 獲取總盈虧
   virtual double GetTotalProfit() = 0;
   
   // 獲取指定類型的總盈虧
   virtual double GetTotalProfitByType(ENUM_MARTINGALE_GROUP_TYPE groupType) = 0;
   
   // 獲取訂單組總數
   virtual int GetGroupCount() = 0;
   
   // 獲取指定類型的訂單組數量
   virtual int GetGroupCountByType(ENUM_MARTINGALE_GROUP_TYPE groupType) = 0;
   
   // 檢查訂單組是否存在
   virtual bool GroupExists(int groupId) = 0;
   
   // 清空所有訂單組
   virtual void ClearAllGroups() = 0;
   
   // 獲取管理器統計信息
   virtual string GetStatistics() = 0;
   
   // 設置所有訂單組的止盈
   virtual void SetAllGroupsTP(double profit) = 0;
   
   // 設置所有訂單組的止損
   virtual void SetAllGroupsSL(double loss) = 0;
   
   // 設置指定類型訂單組的止盈
   virtual void SetGroupsTPByType(ENUM_MARTINGALE_GROUP_TYPE groupType, double profit) = 0;
   
   // 設置指定類型訂單組的止損
   virtual void SetGroupsSLByType(ENUM_MARTINGALE_GROUP_TYPE groupType, double loss) = 0;
};
