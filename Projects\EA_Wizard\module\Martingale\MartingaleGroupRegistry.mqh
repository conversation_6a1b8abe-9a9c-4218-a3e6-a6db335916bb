#property strict

#include "interface/IMartingaleGroupRegistry.mqh"
#include "../mql4-lib-master/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組註冊器實現                                         |
//+------------------------------------------------------------------+
class MartingaleGroupRegistry : public IMartingaleGroupRegistry
{
private:
   HashMap<int, MartingaleOrderGroup*> m_groups;    // 訂單組存儲
   int m_lastRegisteredId;                          // 最後註冊的ID
   int m_maxGroups;                                 // 最大訂單組數量
   string m_name;                                   // 註冊器名稱
   
public:
   // 構造函數
   MartingaleGroupRegistry(string name = "MartingaleGroupRegistry", int maxGroups = 100)
      : m_groups(false), // 不擁有對象，由管理器負責釋放
        m_lastRegisteredId(-1),
        m_maxGroups(maxGroups),
        m_name(name)
   {
   }
   
   // 析構函數
   ~MartingaleGroupRegistry()
   {
      // 不需要刪除訂單組，因為HashMap設置為不擁有對象
   }
   
   // 註冊訂單組
   virtual bool Register(int groupId, MartingaleOrderGroup* group) override
   {
      if(group == NULL)
      {
         Print("馬丁格爾註冊器[", m_name, "] 註冊失敗: 訂單組為NULL");
         return false;
      }
      
      if(m_groups.size() >= m_maxGroups)
      {
         Print("馬丁格爾註冊器[", m_name, "] 註冊失敗: 已達到最大數量 ", m_maxGroups);
         return false;
      }
      
      if(m_groups.contains(groupId))
      {
         Print("馬丁格爾註冊器[", m_name, "] 註冊失敗: ID ", groupId, " 已存在");
         return false;
      }
      
      m_groups.set(groupId, group);
      m_lastRegisteredId = groupId;
      
      Print("馬丁格爾註冊器[", m_name, "] 成功註冊訂單組 ID: ", groupId, 
            ", 類型: ", group.getGroupTypeString());
      
      return true;
   }
   
   // 註銷訂單組
   virtual bool Unregister(int groupId) override
   {
      if(!m_groups.contains(groupId))
      {
         Print("馬丁格爾註冊器[", m_name, "] 註銷失敗: ID ", groupId, " 不存在");
         return false;
      }
      
      m_groups.unset(groupId);
      Print("馬丁格爾註冊器[", m_name, "] 成功註銷訂單組 ID: ", groupId);
      
      return true;
   }
   
   // 查找訂單組
   virtual MartingaleOrderGroup* Find(int groupId) override
   {
      if(!m_groups.contains(groupId))
      {
         return NULL;
      }
      
      return m_groups.get(groupId, NULL);
   }
   
   // 檢查訂單組是否存在
   virtual bool Contains(int groupId) override
   {
      return m_groups.contains(groupId);
   }
   
   // 獲取所有訂單組的HashMap
   virtual HashMap<int, MartingaleOrderGroup*>* GetAll() override
   {
      return &m_groups;
   }
   
   // 獲取所有訂單組ID
   virtual void GetAllIds(int &ids[]) override
   {
      int count = m_groups.size();
      ArrayResize(ids, count);
      
      int index = 0;
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = m_groups.begin(); 
          iter != m_groups.end(); 
          ++iter)
      {
         if(index < count)
         {
            ids[index] = iter.key();
            index++;
         }
      }
   }
   
   // 清空註冊器
   virtual void Clear() override
   {
      int oldSize = m_groups.size();
      m_groups.clear();
      m_lastRegisteredId = -1;
      
      Print("馬丁格爾註冊器[", m_name, "] 已清空 ", oldSize, " 個訂單組");
   }
   
   // 獲取註冊的訂單組數量
   virtual int GetCount() override
   {
      return m_groups.size();
   }
   
   // 檢查註冊器是否為空
   virtual bool IsEmpty() override
   {
      return m_groups.size() == 0;
   }
   
   // 獲取最後註冊的訂單組ID
   virtual int GetLastRegisteredId() override
   {
      return m_lastRegisteredId;
   }
   
   // 更新訂單組
   virtual bool Update(int groupId, MartingaleOrderGroup* group) override
   {
      if(group == NULL)
      {
         Print("馬丁格爾註冊器[", m_name, "] 更新失敗: 訂單組為NULL");
         return false;
      }
      
      if(!m_groups.contains(groupId))
      {
         Print("馬丁格爾註冊器[", m_name, "] 更新失敗: ID ", groupId, " 不存在");
         return false;
      }
      
      m_groups.set(groupId, group);
      Print("馬丁格爾註冊器[", m_name, "] 成功更新訂單組 ID: ", groupId);
      
      return true;
   }
   
   // 遍歷所有訂單組（使用回調函數）
   virtual void ForEach(void (*callback)(int, MartingaleOrderGroup*)) override
   {
      if(callback == NULL)
      {
         Print("馬丁格爾註冊器[", m_name, "] 遍歷失敗: 回調函數為NULL");
         return;
      }
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = m_groups.begin(); 
          iter != m_groups.end(); 
          ++iter)
      {
         callback(iter.key(), iter.value());
      }
   }
   
   // 獲取註冊器統計信息
   virtual string GetStatistics() override
   {
      string stats = StringFormat(
         "馬丁格爾註冊器[%s] 統計:\n" +
         "- 總訂單組數: %d/%d\n" +
         "- 最後註冊ID: %d\n" +
         "- 使用率: %.1f%%",
         m_name,
         m_groups.size(),
         m_maxGroups,
         m_lastRegisteredId,
         (double)m_groups.size() / m_maxGroups * 100.0
      );
      
      return stats;
   }
   
   // 獲取註冊器名稱
   string GetName() const { return m_name; }
   
   // 設置最大訂單組數量
   void SetMaxGroups(int maxGroups)
   {
      if(maxGroups > 0)
      {
         m_maxGroups = maxGroups;
         Print("馬丁格爾註冊器[", m_name, "] 設置最大訂單組數量: ", maxGroups);
      }
   }
   
   // 獲取最大訂單組數量
   int GetMaxGroups() const { return m_maxGroups; }
};
