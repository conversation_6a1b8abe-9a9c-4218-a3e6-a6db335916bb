# 馬丁格爾訂單組更新說明

## 概述
本次更新為 `MartingaleOrderGroup` 類添加了訂單組獨立ID和訂單組類型功能，提升了馬丁格爾策略的管理能力。

## 主要更新內容

### 1. 新增枚舉類型
```cpp
enum ENUM_MARTINGALE_GROUP_TYPE
{
   MARTINGALE_TYPE_STANDARD,     // 標準馬丁格爾
   MARTINGALE_TYPE_REVERSE,      // 反向馬丁格爾
   MARTINGALE_TYPE_GRID,         // 網格馬丁格爾
   MARTINGALE_TYPE_PROGRESSIVE,  // 漸進式馬丁格爾
   MARTINGALE_TYPE_CUSTOM        // 自定義馬丁格爾
};
```

### 2. 新增成員變量
- `int m_groupId`: 訂單組獨立ID
- `ENUM_MARTINGALE_GROUP_TYPE m_groupType`: 訂單組類型
- `static int s_nextGroupId`: 靜態ID計數器

### 3. 更新構造函數
構造函數現在支持指定訂單組類型：
```cpp
MartingaleOrderGroup(string symbol,
                     ENUM_MARTINGALE_GROUP_TYPE groupType = MARTINGALE_TYPE_STANDARD,
                     double initialLots = 0.01,
                     double multiplier = 2.0,
                     double gridStep = 20.0,
                     int maxLevel = 5,
                     bool isReversed = false,
                     int magicNumber = 0)
```

### 4. 新增公共方法
- `int getGroupId() const`: 獲取訂單組ID
- `ENUM_MARTINGALE_GROUP_TYPE getGroupType() const`: 獲取訂單組類型
- `void setGroupType(ENUM_MARTINGALE_GROUP_TYPE groupType)`: 設置訂單組類型
- `string getGroupTypeString() const`: 獲取訂單組類型字符串描述

### 5. 新增私有方法
- `static int GenerateGroupId()`: 生成唯一的訂單組ID
- `static void InitializeIdCounter()`: 初始化靜態ID計數器

## ID生成機制
- 使用靜態計數器確保每個訂單組都有唯一的ID
- 初始ID從10000開始
- 每創建一個新的訂單組，ID自動遞增
- 提供初始化方法可以根據時間戳重置ID計數器

## 訂單組類型說明
1. **標準馬丁格爾 (MARTINGALE_TYPE_STANDARD)**: 傳統的馬丁格爾策略
2. **反向馬丁格爾 (MARTINGALE_TYPE_REVERSE)**: 反向馬丁格爾策略
3. **網格馬丁格爾 (MARTINGALE_TYPE_GRID)**: 網格式馬丁格爾策略
4. **漸進式馬丁格爾 (MARTINGALE_TYPE_PROGRESSIVE)**: 漸進式馬丁格爾策略
5. **自定義馬丁格爾 (MARTINGALE_TYPE_CUSTOM)**: 用戶自定義的馬丁格爾策略

## 使用示例
```cpp
// 創建標準馬丁格爾訂單組
MartingaleOrderGroup* standardGroup = new MartingaleOrderGroup("EURUSD", MARTINGALE_TYPE_STANDARD);

// 創建反向馬丁格爾訂單組
MartingaleOrderGroup* reverseGroup = new MartingaleOrderGroup("GBPUSD", MARTINGALE_TYPE_REVERSE, 0.02, 1.5);

// 獲取訂單組信息
int groupId = standardGroup.getGroupId();
ENUM_MARTINGALE_GROUP_TYPE groupType = standardGroup.getGroupType();
string typeDescription = standardGroup.getGroupTypeString();

// 動態修改訂單組類型
standardGroup.setGroupType(MARTINGALE_TYPE_GRID);
```

## 測試
已創建完整的測試套件：
- `MartingaleOrderGroupTest.mqh`: 測試類
- `TestRunner.mq4`: 測試運行腳本

測試覆蓋：
- ID生成唯一性測試
- 訂單組類型設置和獲取測試
- 默認參數測試
- 類型字符串描述測試

## 向後兼容性
- 所有現有的構造函數調用仍然有效
- 新增的參數都有默認值
- 不會影響現有的馬丁格爾策略邏輯

## 注意事項
1. 靜態ID計數器在程序重啟後會重置
2. 如需持久化ID，建議使用 `InitializeIdCounter()` 方法
3. 訂單組類型可以在運行時動態修改
4. ID一旦生成就不會改變，確保了訂單組的唯一標識

## 文件結構
```
Projects\EA_Wizard\module\Martingale\
├── MartingaleOrderGroup.mqh          # 主要類文件（已更新）
├── test\
│   ├── MartingaleOrderGroupTest.mqh  # 測試類（新增）
│   └── TestRunner.mq4                # 測試運行腳本（新增）
└── README_MartingaleOrderGroup_Updates.md  # 本說明文件（新增）
```
