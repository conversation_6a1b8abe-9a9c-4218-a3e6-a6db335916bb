#property strict

#include "../MartingaleOrderGroup.mqh"
#include "../../mql4-lib-master/Collection/HashMap.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組註冊器介面                                         |
//+------------------------------------------------------------------+
interface IMartingaleGroupRegistry
{
public:
   // 註冊訂單組
   virtual bool Register(int groupId, MartingaleOrderGroup* group) = 0;
   
   // 註銷訂單組
   virtual bool Unregister(int groupId) = 0;
   
   // 查找訂單組
   virtual MartingaleOrderGroup* Find(int groupId) = 0;
   
   // 檢查訂單組是否存在
   virtual bool Contains(int groupId) = 0;
   
   // 獲取所有訂單組的HashMap
   virtual HashMap<int, MartingaleOrderGroup*>* GetAll() = 0;
   
   // 獲取所有訂單組ID
   virtual void GetAllIds(int &ids[]) = 0;
   
   // 清空註冊器
   virtual void Clear() = 0;
   
   // 獲取註冊的訂單組數量
   virtual int GetCount() = 0;
   
   // 檢查註冊器是否為空
   virtual bool IsEmpty() = 0;
   
   // 獲取最後註冊的訂單組ID
   virtual int GetLastRegisteredId() = 0;
   
   // 更新訂單組
   virtual bool Update(int groupId, MartingaleOrderGroup* group) = 0;
   
   // 遍歷所有訂單組（使用回調函數）
   virtual void ForEach(void (*callback)(int, MartingaleOrderGroup*)) = 0;
   
   // 獲取註冊器統計信息
   virtual string GetStatistics() = 0;
};
