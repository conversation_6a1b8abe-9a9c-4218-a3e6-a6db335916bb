#property strict

#include "MartingaleOrderGroup.mqh"
#include "MartingaleGroupManager.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組工廠類                                             |
//+------------------------------------------------------------------+
class MartingaleGroupFactory
{
private:
   static MartingaleGroupManager* s_manager;        // 管理器實例
   static int s_defaultMagicBase;                   // 默認魔術數字基數
   
public:
   // 初始化工廠
   static void Initialize(MartingaleGroupManager* manager = NULL)
   {
      if(manager == NULL)
      {
         s_manager = MartingaleGroupManager::GetInstance();
      }
      else
      {
         s_manager = manager;
      }
      
      s_defaultMagicBase = 20000; // 馬丁格爾魔術數字從20000開始
      
      Print("馬丁格爾工廠已初始化，管理器: ", s_manager.GetName());
   }
   
   // 創建標準馬丁格爾訂單組
   static MartingaleOrderGroup* CreateStandardGroup(
      string symbol,
      double initialLots = 0.01,
      double multiplier = 2.0,
      double gridStep = 20.0,
      int maxLevel = 5,
      bool isReversed = false,
      int magicNumber = 0
   )
   {
      if(magicNumber == 0)
      {
         magicNumber = GenerateUniqueMagicNumber();
      }
      
      MartingaleOrderGroup* group = new MartingaleOrderGroup(
         symbol,
         MARTINGALE_TYPE_STANDARD,
         initialLots,
         multiplier,
         gridStep,
         maxLevel,
         isReversed,
         magicNumber
      );
      
      if(s_manager != NULL && !s_manager.RegisterGroup(group))
      {
         delete group;
         return NULL;
      }
      
      Print("工廠創建標準馬丁格爾訂單組 - ID: ", group.getGroupId(), 
            ", 品種: ", symbol, ", 魔術數字: ", magicNumber);
      
      return group;
   }
   
   // 創建反向馬丁格爾訂單組
   static MartingaleOrderGroup* CreateReverseGroup(
      string symbol,
      double initialLots = 0.04,
      double multiplier = 2.0,
      double gridStep = 25.0,
      int maxLevel = 4,
      int magicNumber = 0
   )
   {
      if(magicNumber == 0)
      {
         magicNumber = GenerateUniqueMagicNumber();
      }
      
      MartingaleOrderGroup* group = new MartingaleOrderGroup(
         symbol,
         MARTINGALE_TYPE_REVERSE,
         initialLots,
         multiplier,
         gridStep,
         maxLevel,
         true, // 反向模式
         magicNumber
      );
      
      if(s_manager != NULL && !s_manager.RegisterGroup(group))
      {
         delete group;
         return NULL;
      }
      
      Print("工廠創建反向馬丁格爾訂單組 - ID: ", group.getGroupId(), 
            ", 品種: ", symbol, ", 魔術數字: ", magicNumber);
      
      return group;
   }
   
   // 創建網格馬丁格爾訂單組
   static MartingaleOrderGroup* CreateGridGroup(
      string symbol,
      double initialLots = 0.01,
      double multiplier = 1.5,
      double gridStep = 15.0,
      int maxLevel = 6,
      int magicNumber = 0
   )
   {
      if(magicNumber == 0)
      {
         magicNumber = GenerateUniqueMagicNumber();
      }
      
      MartingaleOrderGroup* group = new MartingaleOrderGroup(
         symbol,
         MARTINGALE_TYPE_GRID,
         initialLots,
         multiplier,
         gridStep,
         maxLevel,
         false,
         magicNumber
      );
      
      if(s_manager != NULL && !s_manager.RegisterGroup(group))
      {
         delete group;
         return NULL;
      }
      
      Print("工廠創建網格馬丁格爾訂單組 - ID: ", group.getGroupId(), 
            ", 品種: ", symbol, ", 魔術數字: ", magicNumber);
      
      return group;
   }
   
   // 創建漸進式馬丁格爾訂單組
   static MartingaleOrderGroup* CreateProgressiveGroup(
      string symbol,
      double initialLots = 0.01,
      double multiplier = 1.3,
      double gridStep = 25.0,
      int maxLevel = 8,
      int magicNumber = 0
   )
   {
      if(magicNumber == 0)
      {
         magicNumber = GenerateUniqueMagicNumber();
      }
      
      MartingaleOrderGroup* group = new MartingaleOrderGroup(
         symbol,
         MARTINGALE_TYPE_PROGRESSIVE,
         initialLots,
         multiplier,
         gridStep,
         maxLevel,
         false,
         magicNumber
      );
      
      if(s_manager != NULL && !s_manager.RegisterGroup(group))
      {
         delete group;
         return NULL;
      }
      
      Print("工廠創建漸進式馬丁格爾訂單組 - ID: ", group.getGroupId(), 
            ", 品種: ", symbol, ", 魔術數字: ", magicNumber);
      
      return group;
   }
   
   // 創建自定義馬丁格爾訂單組
   static MartingaleOrderGroup* CreateCustomGroup(
      string symbol,
      double initialLots,
      double multiplier,
      double gridStep,
      int maxLevel,
      bool isReversed,
      int magicNumber = 0
   )
   {
      if(magicNumber == 0)
      {
         magicNumber = GenerateUniqueMagicNumber();
      }
      
      MartingaleOrderGroup* group = new MartingaleOrderGroup(
         symbol,
         MARTINGALE_TYPE_CUSTOM,
         initialLots,
         multiplier,
         gridStep,
         maxLevel,
         isReversed,
         magicNumber
      );
      
      if(s_manager != NULL && !s_manager.RegisterGroup(group))
      {
         delete group;
         return NULL;
      }
      
      Print("工廠創建自定義馬丁格爾訂單組 - ID: ", group.getGroupId(), 
            ", 品種: ", symbol, ", 魔術數字: ", magicNumber);
      
      return group;
   }
   
   // 批量創建多個訂單組
   static bool CreateMultipleGroups(
      string symbols[],
      ENUM_MARTINGALE_GROUP_TYPE groupTypes[],
      double initialLots = 0.01,
      double multiplier = 2.0,
      double gridStep = 20.0,
      int maxLevel = 5
   )
   {
      int symbolCount = ArraySize(symbols);
      int typeCount = ArraySize(groupTypes);
      
      if(symbolCount == 0 || typeCount == 0)
      {
         Print("工廠批量創建失敗: 參數數組為空");
         return false;
      }
      
      int createdCount = 0;
      
      for(int i = 0; i < symbolCount; i++)
      {
         ENUM_MARTINGALE_GROUP_TYPE groupType = groupTypes[i % typeCount]; // 循環使用類型
         MartingaleOrderGroup* group = NULL;
         
         switch(groupType)
         {
            case MARTINGALE_TYPE_STANDARD:
               group = CreateStandardGroup(symbols[i], initialLots, multiplier, gridStep, maxLevel);
               break;
               
            case MARTINGALE_TYPE_REVERSE:
               group = CreateReverseGroup(symbols[i], initialLots * 2, multiplier, gridStep + 5, maxLevel - 1);
               break;
               
            case MARTINGALE_TYPE_GRID:
               group = CreateGridGroup(symbols[i], initialLots, multiplier * 0.75, gridStep - 5, maxLevel + 1);
               break;
               
            case MARTINGALE_TYPE_PROGRESSIVE:
               group = CreateProgressiveGroup(symbols[i], initialLots, multiplier * 0.65, gridStep + 5, maxLevel + 3);
               break;
               
            case MARTINGALE_TYPE_CUSTOM:
               group = CreateCustomGroup(symbols[i], initialLots, multiplier, gridStep, maxLevel, false);
               break;
         }
         
         if(group != NULL)
         {
            createdCount++;
         }
      }
      
      Print("工廠批量創建完成 - 成功: ", createdCount, "/", symbolCount);
      
      return createdCount == symbolCount;
   }
   
   // 生成唯一的魔術數字
   static int GenerateUniqueMagicNumber()
   {
      static int counter = 0;
      counter++;
      
      // 使用時間戳和計數器確保唯一性
      int timestamp = (int)(TimeCurrent() % 10000);
      int magicNumber = s_defaultMagicBase + timestamp + counter;
      
      return magicNumber;
   }
   
   // 設置默認魔術數字基數
   static void SetDefaultMagicBase(int magicBase)
   {
      if(magicBase > 0)
      {
         s_defaultMagicBase = magicBase;
         Print("工廠設置默認魔術數字基數: ", magicBase);
      }
   }
   
   // 獲取默認魔術數字基數
   static int GetDefaultMagicBase()
   {
      return s_defaultMagicBase;
   }
   
   // 獲取管理器
   static MartingaleGroupManager* GetManager()
   {
      return s_manager;
   }
   
   // 清理工廠
   static void Cleanup()
   {
      if(s_manager != NULL)
      {
         s_manager.ClearAllGroups();
      }
      
      Print("馬丁格爾工廠已清理");
   }
   
   // 獲取工廠統計信息
   static string GetStatistics()
   {
      if(s_manager == NULL)
      {
         return "工廠未初始化";
      }
      
      string stats = StringFormat(
         "馬丁格爾工廠統計:\n" +
         "- 默認魔術數字基數: %d\n" +
         "- 管理器: %s\n" +
         "%s",
         s_defaultMagicBase,
         s_manager.GetName(),
         s_manager.GetStatistics()
      );
      
      return stats;
   }
};

// 靜態成員初始化
static MartingaleGroupManager* MartingaleGroupFactory::s_manager = NULL;
static int MartingaleGroupFactory::s_defaultMagicBase = 20000;
