#property strict

#include "interface/IMartingaleGroupManager.mqh"
#include "MartingaleGroupRegistry.mqh"
#include "../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組管理器實現                                         |
//+------------------------------------------------------------------+
class MartingaleGroupManager : public IMartingaleGroupManager
{
private:
   static MartingaleGroupManager* s_instance;       // 單例實例
   MartingaleGroupRegistry* m_registry;             // 註冊器
   bool m_registryOwned;                            // 是否擁有註冊器
   string m_name;                                   // 管理器名稱
   
   // 私有構造函數（單例模式）
   MartingaleGroupManager(string name = "MartingaleGroupManager")
      : m_name(name), m_registryOwned(true)
   {
      m_registry = new MartingaleGroupRegistry(name + "_Registry");
   }
   
public:
   // 獲取單例實例
   static MartingaleGroupManager* GetInstance()
   {
      if(s_instance == NULL)
      {
         s_instance = new MartingaleGroupManager();
      }
      return s_instance;
   }
   
   // 析構函數
   ~MartingaleGroupManager()
   {
      // 清理所有訂單組
      ClearAllGroups();
      
      // 清理註冊器
      if(m_registryOwned && m_registry != NULL)
      {
         delete m_registry;
         m_registry = NULL;
      }
   }
   
   // 註冊訂單組
   virtual bool RegisterGroup(MartingaleOrderGroup* group) override
   {
      if(group == NULL)
      {
         Print("馬丁格爾管理器[", m_name, "] 註冊失敗: 訂單組為NULL");
         return false;
      }
      
      int groupId = group.getGroupId();
      
      if(m_registry.Contains(groupId))
      {
         Print("馬丁格爾管理器[", m_name, "] 註冊失敗: 訂單組ID ", groupId, " 已存在");
         return false;
      }
      
      bool success = m_registry.Register(groupId, group);
      
      if(success)
      {
         Print("馬丁格爾管理器[", m_name, "] 成功註冊訂單組 ID: ", groupId, 
               ", 類型: ", group.getGroupTypeString());
      }
      
      return success;
   }
   
   // 註銷訂單組
   virtual bool UnregisterGroup(int groupId) override
   {
      MartingaleOrderGroup* group = m_registry.Find(groupId);
      if(group == NULL)
      {
         Print("馬丁格爾管理器[", m_name, "] 註銷失敗: 訂單組ID ", groupId, " 不存在");
         return false;
      }
      
      // 先從註冊器中移除
      bool success = m_registry.Unregister(groupId);
      
      if(success)
      {
         // 刪除訂單組對象
         delete group;
         Print("馬丁格爾管理器[", m_name, "] 成功註銷並刪除訂單組 ID: ", groupId);
      }
      
      return success;
   }
   
   // 根據ID獲取訂單組
   virtual MartingaleOrderGroup* GetGroup(int groupId) override
   {
      return m_registry.Find(groupId);
   }
   
   // 根據類型獲取訂單組列表
   virtual Vector<MartingaleOrderGroup*>* GetGroupsByType(ENUM_MARTINGALE_GROUP_TYPE groupType) override
   {
      Vector<MartingaleOrderGroup*>* result = new Vector<MartingaleOrderGroup*>(false); // 不擁有對象
      
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL && group.getGroupType() == groupType)
         {
            result.add(group);
         }
      }
      
      return result;
   }
   
   // 根據交易品種獲取訂單組列表
   virtual Vector<MartingaleOrderGroup*>* GetGroupsBySymbol(string symbol) override
   {
      Vector<MartingaleOrderGroup*>* result = new Vector<MartingaleOrderGroup*>(false);
      
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            // 注意：這裡需要比較交易品種，但由於m_symbol是私有的，我們暫時跳過
            // 在實際實現中，可能需要在MartingaleOrderGroup中添加getSymbol()方法
            result.add(group);
         }
      }
      
      return result;
   }
   
   // 根據魔術數字獲取訂單組列表
   virtual Vector<MartingaleOrderGroup*>* GetGroupsByMagic(int magicNumber) override
   {
      Vector<MartingaleOrderGroup*>* result = new Vector<MartingaleOrderGroup*>(false);
      
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            // 注意：這裡需要比較魔術數字，但由於m_magicNumber是私有的，我們暫時跳過
            // 在實際實現中，可能需要在MartingaleOrderGroup中添加getMagicNumber()方法
            result.add(group);
         }
      }
      
      return result;
   }
   
   // 獲取所有訂單組
   virtual Vector<MartingaleOrderGroup*>* GetAllGroups() override
   {
      Vector<MartingaleOrderGroup*>* result = new Vector<MartingaleOrderGroup*>(false);
      
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            result.add(group);
         }
      }
      
      return result;
   }
   
   // 關閉所有訂單組
   virtual bool CloseAllGroups() override
   {
      bool allSuccess = true;
      int closedCount = 0;
      
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            if(group.closeAll())
            {
               closedCount++;
            }
            else
            {
               allSuccess = false;
            }
         }
      }
      
      Print("馬丁格爾管理器[", m_name, "] 關閉訂單組完成 - 成功: ", closedCount, 
            "/", m_registry.GetCount());
      
      return allSuccess;
   }
   
   // 關閉指定類型的所有訂單組
   virtual bool CloseGroupsByType(ENUM_MARTINGALE_GROUP_TYPE groupType) override
   {
      Vector<MartingaleOrderGroup*>* groups = GetGroupsByType(groupType);
      bool allSuccess = true;
      int closedCount = 0;
      
      for(int i = 0; i < groups.size(); i++)
      {
         MartingaleOrderGroup* group = groups.get(i);
         if(group != NULL)
         {
            if(group.closeAll())
            {
               closedCount++;
            }
            else
            {
               allSuccess = false;
            }
         }
      }
      
      Print("馬丁格爾管理器[", m_name, "] 關閉指定類型訂單組完成 - 成功: ", closedCount, 
            "/", groups.size());
      
      delete groups;
      return allSuccess;
   }
   
   // 更新所有訂單組狀態
   virtual void UpdateAllGroups() override
   {
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            group.updateStatus();
         }
      }
   }
   
   // 獲取總盈虧
   virtual double GetTotalProfit() override
   {
      double totalProfit = 0.0;
      
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            totalProfit += group.getTotalProfit();
         }
      }
      
      return totalProfit;
   }
   
   // 獲取指定類型的總盈虧
   virtual double GetTotalProfitByType(ENUM_MARTINGALE_GROUP_TYPE groupType) override
   {
      double totalProfit = 0.0;
      Vector<MartingaleOrderGroup*>* groups = GetGroupsByType(groupType);
      
      for(int i = 0; i < groups.size(); i++)
      {
         MartingaleOrderGroup* group = groups.get(i);
         if(group != NULL)
         {
            totalProfit += group.getTotalProfit();
         }
      }
      
      delete groups;
      return totalProfit;
   }
   
   // 獲取訂單組總數
   virtual int GetGroupCount() override
   {
      return m_registry.GetCount();
   }
   
   // 獲取指定類型的訂單組數量
   virtual int GetGroupCountByType(ENUM_MARTINGALE_GROUP_TYPE groupType) override
   {
      Vector<MartingaleOrderGroup*>* groups = GetGroupsByType(groupType);
      int count = groups.size();
      delete groups;
      return count;
   }
   
   // 檢查訂單組是否存在
   virtual bool GroupExists(int groupId) override
   {
      return m_registry.Contains(groupId);
   }
   
   // 清空所有訂單組
   virtual void ClearAllGroups() override
   {
      // 先關閉所有訂單
      CloseAllGroups();
      
      // 刪除所有訂單組對象
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            delete group;
         }
      }
      
      // 清空註冊器
      m_registry.Clear();
      
      Print("馬丁格爾管理器[", m_name, "] 已清空所有訂單組");
   }
   
   // 獲取管理器統計信息
   virtual string GetStatistics() override
   {
      UpdateAllGroups(); // 先更新所有狀態
      
      string stats = StringFormat(
         "馬丁格爾管理器[%s] 統計:\n" +
         "- 總訂單組數: %d\n" +
         "- 總盈虧: %.2f\n" +
         "- 標準馬丁格爾: %d 組\n" +
         "- 反向馬丁格爾: %d 組\n" +
         "- 網格馬丁格爾: %d 組\n" +
         "- 漸進式馬丁格爾: %d 組\n" +
         "- 自定義馬丁格爾: %d 組",
         m_name,
         GetGroupCount(),
         GetTotalProfit(),
         GetGroupCountByType(MARTINGALE_TYPE_STANDARD),
         GetGroupCountByType(MARTINGALE_TYPE_REVERSE),
         GetGroupCountByType(MARTINGALE_TYPE_GRID),
         GetGroupCountByType(MARTINGALE_TYPE_PROGRESSIVE),
         GetGroupCountByType(MARTINGALE_TYPE_CUSTOM)
      );
      
      return stats;
   }
   
   // 設置所有訂單組的止盈
   virtual void SetAllGroupsTP(double profit) override
   {
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            group.setGroupTP(profit);
         }
      }
      
      Print("馬丁格爾管理器[", m_name, "] 為所有訂單組設置止盈: ", profit);
   }
   
   // 設置所有訂單組的止損
   virtual void SetAllGroupsSL(double loss) override
   {
      HashMap<int, MartingaleOrderGroup*>* allGroups = m_registry.GetAll();
      
      for(HashMapIterator<int, MartingaleOrderGroup*> iter = allGroups.begin(); 
          iter != allGroups.end(); 
          ++iter)
      {
         MartingaleOrderGroup* group = iter.value();
         if(group != NULL)
         {
            group.setGroupSL(loss);
         }
      }
      
      Print("馬丁格爾管理器[", m_name, "] 為所有訂單組設置止損: ", loss);
   }
   
   // 設置指定類型訂單組的止盈
   virtual void SetGroupsTPByType(ENUM_MARTINGALE_GROUP_TYPE groupType, double profit) override
   {
      Vector<MartingaleOrderGroup*>* groups = GetGroupsByType(groupType);
      
      for(int i = 0; i < groups.size(); i++)
      {
         MartingaleOrderGroup* group = groups.get(i);
         if(group != NULL)
         {
            group.setGroupTP(profit);
         }
      }
      
      Print("馬丁格爾管理器[", m_name, "] 為指定類型訂單組設置止盈: ", profit, 
            ", 影響 ", groups.size(), " 個訂單組");
      
      delete groups;
   }
   
   // 設置指定類型訂單組的止損
   virtual void SetGroupsSLByType(ENUM_MARTINGALE_GROUP_TYPE groupType, double loss) override
   {
      Vector<MartingaleOrderGroup*>* groups = GetGroupsByType(groupType);
      
      for(int i = 0; i < groups.size(); i++)
      {
         MartingaleOrderGroup* group = groups.get(i);
         if(group != NULL)
         {
            group.setGroupSL(loss);
         }
      }
      
      Print("馬丁格爾管理器[", m_name, "] 為指定類型訂單組設置止損: ", loss, 
            ", 影響 ", groups.size(), " 個訂單組");
      
      delete groups;
   }
   
   // 獲取註冊器
   MartingaleGroupRegistry* GetRegistry() { return m_registry; }
   
   // 獲取管理器名稱
   string GetName() const { return m_name; }
};

// 靜態成員初始化
static MartingaleGroupManager* MartingaleGroupManager::s_instance = NULL;
